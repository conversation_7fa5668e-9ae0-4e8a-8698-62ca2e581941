<template>
  <n-button-group>
    <n-button
      v-for="(btn, index) in buttons"
      :key="index"
      :class="{ active: currentIndex === index }"
      @click="currentIndex = index"
    >
      {{ btn.text }}
    </n-button>
  </n-button-group>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { NButton, NButtonGroup } from 'naive-ui';

export default defineComponent({
  components: {
    NButton,
    NButtonGroup
  },
  setup() {
    const buttons = [
      { text: '预览' },
      { text: '详情' }
    ];
    const currentIndex = ref(0);
    return {
      buttons,
      currentIndex
    };
  }
});
</script>

<style scoped>
.n-button-group {
  display: flex;
}

.n-button {
  width: 54px;
  height: 28px;
  flex-shrink: 0;
  border-radius: 6px;
  border: 1px solid var(--icon_Blue, #0085FF);
  color: var(--icon_Blue, #0085FF);
  text-align: center;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition: all 0.3s ease;
}

.n-button.active {
  background: var(--icon_Blue, #0085FF);
  color: white;
  box-shadow: none;
}
.n-button.hover {
   box-shadow: none;
}
</style>
