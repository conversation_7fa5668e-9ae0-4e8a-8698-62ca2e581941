<template>
  <div class="flex h-full overflow-hidden bg-[#f9f9f9]">
    <!-- 左侧时间线区域 -->
    <div class="w-200px my-20 flex-shrink-0   overflow-y-auto">
      <div class="p-4">
        <n-timeline>
          <n-timeline-item
            v-for="(stage, index) in timelineData"
            :key="stage.id"
            :type="getTimelineType(index)"
            :color="getTimelineColor(index)"
            :title="stage.title"
            class="cursor-pointer timeline-item"
            :class="{ 'timeline-item-active': activeStageId === stage.id }"
            :line-type="activeStageId === stage.id ? 'dashed' : 'default'"
            @click="enhancedHandleTimelineClick(stage.id)"
          >
            <template #icon>
              <div
                class="timeline-icon"
                :class="{ 'timeline-icon-active': activeStageId === stage.id }"
                :style="{
                  backgroundColor: activeStageId === stage.id ? getTimelineColor(index) : 'transparent',
                  borderColor: activeStageId === stage.id ? getTimelineColor(index) : '#B8B8B8'
                }"
              ></div>
            </template>
            <template #header>
              <div
                class="timeline-header"
                :class="{ 'timeline-header-active': activeStageId === stage.id }"
                :style="{
                  backgroundColor: activeStageId === stage.id ? getTimelineColor(index): 'transparent',
                  color: activeStageId === stage.id ? '#fff' : '#B8B8B8'
                }"
              >
                {{ stage.title }}
              </div>
            </template>
            <div
              class="timeline-content"
              :class="{
                'timeline-content-expanded': activeStageId === stage.id,
                'timeline-content-collapsed': activeStageId !== stage.id
              }"
            >
              <div v-for="solution in stage.solutions" :key="solution.solutionId" class=" solution-item">
                <div class="text-sm font-medium text-gray-700">{{ solution.soluName }}</div>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </div>
    </div>

    <!-- 右侧表格区域 -->
    <div class="flex-1 flex flex-col overflow-hidden pr-30">
      <!-- 表格头部 - 固定在顶部 -->
      <div class="sticky top-0 z-10  table-header">
        <div class="table-header-grid">
          <div class="text-center">计划周数</div>
          <div class="text-center">方案名称</div>
          <div class="text-center">方案天数</div>
          <div class="text-center">方案描述</div>
        </div>
      </div>

      <!-- 表格内容 - 可滚动 -->
      <div ref="tableContentRef" class="flex-1 overflow-y-auto">
        <div
          v-for="stage in timelineData"
          :key="stage.id"
          :id="`stage-${stage.id}`"
          class="table-group"

        >
          <!-- 阶段标题行 -->
          <div
            class="stage-title-row"
            :class="{ 'stage-title-active': activeStageId === stage.id }"
            :style="{ color: activeStageId === stage.id ? getTimelineColor(timelineData.findIndex(item => item.id === stage.id)) : '#B8B8B8' }"
          >
            {{ stage.title }}
          </div>

          <!-- 方案数据行容器 -->
          <div class="solutions-container">
            <div
              v-for="solution in stage.solutions"
              :key="solution.solutionId"
              class="solution-row"
              :class="{ 'solution-row-active': activeStageId === stage.id }"
            >
              <div class="solution-data">
                <div class="solution-field solution-stage">
                  {{ solution.weekCount}}
                </div>
                <n-tooltip trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <div class="solution-field solution-name">
                      {{ solution.soluName }}
                    </div>
                  </template>
                  {{ solution.soluName }}
                </n-tooltip>
                <div class="solution-field solution-duration">
                  {{  }}周 ({{ (solution.soluType === 1 ? solution.dayCount : solution.restDayCount) || 0 }}天)
                </div>
                <n-tooltip trigger="hover" :show-arrow="false">
                  <template #trigger>
                    <div class="solution-field solution-description">
                      <span v-if="getSoluSummaryText(solution.soluSummary)">
                        {{ getSoluSummaryText(solution.soluSummary) }}
                      </span>
                      <span v-if="solution.comment" class="comment-text">
                        {{ solution.comment }}
                      </span>
                    </div>
                  </template>
                  {{ getFullDescriptionText(solution) }}
                </n-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, onMounted, onUnmounted, watch } from 'vue';

// 定义 Props 接口
interface Props {
  anchorItems?: Array<{
    title: string;
    href: string;
    children: Array<{
      title: string;
      href: string;
      weekCount: number;
      days: number | null;
      soluSummary: string[] | string | any;
    }>;
  }>;
}

// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[] | string | any;
    comment: string;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  anchorItems: () => []
});

// 响应式数据
const activeStageId = ref<string>('');
const highlightStageId = ref<string>('');
const tableContentRef = ref<HTMLElement>();

// 时间线节点颜色配置
const timelineColors = ['#868686', '#FFA438', '#638CFD', '#AB9EFF'];

// 转换数据格式
const timelineData = computed<TimelineStage[]>(() => {
  // 如果有真实数据，使用真实数据；否则使用测试数据
  if (props.anchorItems && props.anchorItems.length > 0) {
    return props.anchorItems.map(item => ({
      id: item.href.replace('#', '').replace('.stage', ''),
      title: item.title,
      solutions: item.children.map(child => ({
        solutionId: child.href.replace('#', '').replace('.program', ''),
        soluName: child.title,
        weekCount: child.weekCount,
        dayCount: child.days,
        restDayCount: child.days,
        soluType: 1,
        soluSummary: Array.isArray(child.soluSummary) ? child.soluSummary : (child.soluSummary ? [child.soluSummary] : []),
        comment: ''
      }))
    }));
  }
  return [];
});

// 获取时间线节点颜色
const getTimelineColor = (index: number): string => {
  // 如果超出颜色库数量，使用最后一个颜色
  return index < timelineColors.length ? timelineColors[index] : timelineColors[timelineColors.length - 1];
};

// 获取时间线节点类型
const getTimelineType = (index: number): 'default' | 'info' | 'success' | 'warning' | 'error' => {
  const types = ['default', 'warning', 'info', 'success'] as const;
  return types[index % types.length];
};

// 安全地获取 soluSummary 文本
const getSoluSummaryText = (soluSummary: any): string => {
  if (!soluSummary) return '';

  // 如果是数组，使用 join
  if (Array.isArray(soluSummary)) {
    return soluSummary.join(', ');
  }

  // 如果是字符串，直接返回
  if (typeof soluSummary === 'string') {
    return soluSummary;
  }

  // 其他情况，尝试转换为字符串
  return String(soluSummary);
};

// 获取完整的描述文本用于 tooltip
const getFullDescriptionText = (solution: any): string => {
  const summaryText = getSoluSummaryText(solution.soluSummary);
  const commentText = solution.comment || '';

  if (summaryText && commentText) {
    return `${summaryText} ${commentText}`;
  }

  return summaryText || commentText;
};

// 处理时间线点击事件
const handleTimelineClick = async (stageId: string) => {
  // 切换展开/收起状态
  if (activeStageId.value === stageId) {
    activeStageId.value = ''; // 如果已选中，则收起
    return; // 收起时不执行滚动
  } else {
    activeStageId.value = stageId; // 如果未选中，则展开
  }

  highlightStageId.value = stageId;

  // 滚动到对应的表格组
  await nextTick();
  const targetElement = document.getElementById(`stage-${stageId}`);
  if (targetElement && tableContentRef.value) {
    const containerRect = tableContentRef.value.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const scrollTop = tableContentRef.value.scrollTop + targetRect.top - containerRect.top;

    tableContentRef.value.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });

    // 高亮效果持续2秒后消失
    setTimeout(() => {
      highlightStageId.value = '';
    }, 2000);
  }
};

// 默认不选中任何节点，实现展开/收起功能
// activeStageId.value = '';

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 滚动监听变量
let intersectionObserver: IntersectionObserver | null = null;
let isScrollingProgrammatically = false;

// 处理滚动时的时间线同步
const handleScrollSync = debounce(() => {
  if (isScrollingProgrammatically || !tableContentRef.value) return;

  const container = tableContentRef.value;
  const containerRect = container.getBoundingClientRect();
  const containerTop = containerRect.top;
  const containerHeight = containerRect.height;
  const viewportCenter = containerTop + containerHeight / 2;

  let closestStage = '';
  let closestDistance = Infinity;

  // 查找最接近视口中心的阶段
  timelineData.value.forEach(stage => {
    const element = document.getElementById(`stage-${stage.id}`);
    if (element) {
      const elementRect = element.getBoundingClientRect();
      const elementCenter = elementRect.top + elementRect.height / 2;
      const distance = Math.abs(elementCenter - viewportCenter);

      if (distance < closestDistance) {
        closestDistance = distance;
        closestStage = stage.id;
      }
    }
  });

  // 更新选中状态（但不展开）
  if (closestStage && closestStage !== activeStageId.value) {
    // 这里只更新视觉状态，不触发展开
    const currentActiveId = activeStageId.value;
    activeStageId.value = closestStage;

    // 如果之前没有选中任何节点，则不展开新节点
    if (!currentActiveId) {
      activeStageId.value = '';
    }
  }
}, 150);

// 设置 Intersection Observer
const setupIntersectionObserver = () => {
  console.log(tableContentRef.value)
  if (!tableContentRef.value) return;

  const options = {
    root: tableContentRef.value,
    rootMargin: '-20% 0px -20% 0px', // 只有当元素进入中间60%区域时才触发
    threshold: [0.1, 0.5, 0.9]
  };

  intersectionObserver = new IntersectionObserver((entries) => {
    if (isScrollingProgrammatically) return;

    let mostVisibleEntry: IntersectionObserverEntry | null = null;
    let maxIntersectionRatio = 0;

    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio > maxIntersectionRatio) {
        maxIntersectionRatio = entry.intersectionRatio;
        mostVisibleEntry = entry;
      }
    });

    if (mostVisibleEntry) {
      const stageId = (mostVisibleEntry.target as HTMLElement).id.replace('stage-', '');
      if (stageId && stageId !== activeStageId.value) {
        // 只更新高亮，不自动展开
        highlightStageId.value = stageId;
        setTimeout(() => {
          highlightStageId.value = '';
        }, 1000);
      }
    }
  }, options);

  // 观察所有阶段元素
  timelineData.value.forEach(stage => {
    const element = document.getElementById(`stage-${stage.id}`);
    if (element && intersectionObserver) {
      intersectionObserver.observe(element);
    }
  });
};

// 监听时间线数据变化，设置默认高亮
watch(timelineData, (newData) => {
  if (newData.length > 0 && !activeStageId.value) {
    // 只在没有选中任何项目时才设置默认高亮
    nextTick(() => {
      activeStageId.value = newData[0].id;
    });
  }
}, { immediate: true });

// 组件挂载时设置监听
onMounted(() => {
  nextTick(() => {
    setupIntersectionObserver();

    // 添加滚动监听
    if (tableContentRef.value) {
      tableContentRef.value.addEventListener('scroll', handleScrollSync, { passive: true });
    }
  });
});

// 组件卸载时清理
onUnmounted(() => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
    intersectionObserver = null;
  }

  if (tableContentRef.value) {
    tableContentRef.value.removeEventListener('scroll', handleScrollSync);
  }
});

// 修改原有的点击处理函数，添加程序化滚动标记
const enhanceTimelineClick = () => {
  const originalClick = handleTimelineClick;
  return async (stageId: string) => {
    isScrollingProgrammatically = true;
    await originalClick(stageId);

    // 滚动完成后重置标记
    setTimeout(() => {
      isScrollingProgrammatically = false;
    }, 1000);
  };
};

// 使用增强版的点击处理函数
const enhancedHandleTimelineClick = enhanceTimelineClick();
</script>

<style scoped>
/* 时间线样式 */
.timeline-item {
  transition: all 0.3s ease;
}


/* 移除整体背景色变化 */
.timeline-item-active {
  position: relative;
}

/* 自定义时间线图标 */
.timeline-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid;
  transition: all 0.3s ease;
}

.timeline-icon-active {
  box-shadow: inset 0 0 0 10px currentColor;
}

/* 时间线标题样式 */
.timeline-header {
  color: #B8B8B8;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
  transition: all 0.3s ease;
  padding: 2px 10px;
  border-radius: 6px;
  margin: -4px 0;
  display: inline-block;
  max-width: 260px; /* 考虑到父容器300px减去padding */
  word-wrap: break-word;
}

.timeline-header-active {
  font-weight: 600;
  /* 背景色和文字颜色通过内联样式动态设置 */
}

/* 时间线内容展开/收起动画 */
.timeline-content {
  color: #666;
  transition: all 0.4s ease;
  overflow: hidden;
}

.timeline-content-collapsed {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.timeline-content-expanded {
  max-height: 500px; /* 足够大的高度 */
  opacity: 1;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* 方案项样式 */
.solution-item {
  padding: 6px 0;
  padding-left: 24px; /* 2个字符的左侧缩进 */
  transition: all 0.3s ease;
}

.solution-item .text-sm {
  color: #000;
  font-family: "Source Han Sans CN";
  font-size: 12px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
}

/* 表格头部样式 */
.table-header {
  opacity: 0.9;
  background: #F2F2F2;
}

.table-header-grid {
  display: grid;
  grid-template-columns: 15% 25% 15% 45%;
  padding: 16px 0;
  font-weight: 500;
  color: #4A5568;
}

/* 表格样式 */
.table-group {
  transition: all 0.3s ease;
}

.table-group-highlight {
  background-color: rgba(99, 140, 253, 0.05);
  border-left: 4px solid #638CFD;
}

.table-group-active {
  position: relative;
}

/* 阶段标题行样式 */
.stage-title-row {
  color: #B8B8B8;
  font-family: "Source Han Sans CN";
  font-size: 16px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
  padding: 18px 24px 0 ;
  transition: all 0.3s ease;
}

.stage-title-active {
  font-weight: 500;
}

/* 方案数据行容器 */
.solutions-container {
  padding: 8px 0;
}

/* 方案数据行样式 */
.solution-row {
  margin-bottom: 4px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.75);
  transition: all 0.3s ease;
}

.solution-row-active {
  background: #FFF;
  box-shadow: 2px 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 方案数据网格 */
.solution-data {
  display: grid;
  grid-template-columns: 15% 25% 15% 45%;
  min-height: 40px;
  align-items: center;
  overflow: hidden; /* 防止内容溢出 */
  width: 100%;
}

/* 方案字段样式 */
.solution-field {
  color: #7A7A7A;
  text-align: center;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
}
.solution-row-active .solution-field {
  color: #000;
}

/* 计划阶段列 */
.solution-stage {
  text-align: center;
}

/* 方案名称列 - 添加文字溢出处理 */
.solution-name {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 方案天数列 */
.solution-duration {
  text-align: center;
}

/* 方案描述列 - 添加文字溢出处理 */
.solution-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  min-width: 0; /* 允许 flex 项目收缩到内容宽度以下 */
}

.solution-description .comment-text {
  margin-left: 8px;
  font-size: 12px;
  opacity: 0.8;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  display: none;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 时间线连接线颜色同步 - 默认状态统一使用灰色 */
:deep(.n-timeline .n-timeline-item .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #B8B8B8, #B8B8B8);
}

/* 选中状态的实线连接线 - 使用对应的主题颜色 */
:deep(.n-timeline .n-timeline-item:nth-child(1).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #868686, #868686);
}

:deep(.n-timeline .n-timeline-item:nth-child(2).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #FFA438, #FFA438);
}

:deep(.n-timeline .n-timeline-item:nth-child(3).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #638CFD, #638CFD);
}

:deep(.n-timeline .n-timeline-item:nth-child(4).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #AB9EFF, #AB9EFF);
}

/* 时间线连接线颜色同步 - 虚线状态 */
:deep(.n-timeline .n-timeline-item:nth-child(1).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #868686, #868686 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(2).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #FFA438, #FFA438 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(3).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #638CFD, #638CFD 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(4).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #AB9EFF, #AB9EFF 50%, transparent 50%, transparent 100%);
}
</style>
