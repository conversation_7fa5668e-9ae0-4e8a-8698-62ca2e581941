<template>
  <div class="flex h-full overflow-hidden">
    <!-- 左侧时间线区域 -->
    <div class="w-300px flex-shrink-0 bg-white border-r border-gray-200 overflow-y-auto">
      <div class="p-4">
        <n-timeline>
          <n-timeline-item
            v-for="(stage, index) in timelineData"
            :key="stage.id"
            :type="getTimelineType(index)"
            :color="getTimelineColor(index)"
            :title="stage.title"
            class="cursor-pointer timeline-item"
            :class="{ 'timeline-item-active': activeStageId === stage.id }"
            @click="handleTimelineClick(stage.id)"
          >
            <template #header>
              <div class="timeline-header" :class="{ 'timeline-header-active': activeStageId === stage.id }">
                {{ stage.title }}
              </div>
            </template>
            <div class="timeline-content" :class="{ 'timeline-content-active': activeStageId === stage.id }">
              <div v-for="solution in stage.solutions" :key="solution.solutionId" class="mb-2">
                <div class="text-sm font-medium">{{ solution.soluName }}</div>
                <div class="text-xs text-gray-500">
                  {{ solution.weekCount }}周 | {{ (solution.soluType === 1 ? solution.dayCount : solution.restDayCount) || 0 }}天
                </div>
                <div v-if="solution.soluSummary?.length" class="text-xs text-gray-400 mt-1">
                  {{ solution.soluSummary.join(', ') }}
                </div>
              </div>
            </div>
          </n-timeline-item>
        </n-timeline>
      </div>
    </div>

    <!-- 右侧表格区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 表格头部 - 固定在顶部 -->
      <div class="sticky top-0 z-10 bg-white border-b border-gray-200">
        <div class="grid grid-cols-4 gap-4 p-4 font-medium text-gray-700">
          <div>计划阶段</div>
          <div>方案名称</div>
          <div>方案天数</div>
          <div>方案描述</div>
        </div>
      </div>

      <!-- 表格内容 - 可滚动 -->
      <div ref="tableContentRef" class="flex-1 overflow-y-auto">
        <div
          v-for="stage in timelineData"
          :key="stage.id"
          :id="`stage-${stage.id}`"
          class="table-group"
          :class="{ 'table-group-highlight': highlightStageId === stage.id }"
        >
          <!-- 阶段标题行 -->
          <div class="bg-gray-50 border-b border-gray-200 p-4 font-medium">
            {{ stage.title }}
          </div>

          <!-- 方案数据行 -->
          <div
            v-for="solution in stage.solutions"
            :key="solution.solutionId"
            class="grid grid-cols-4 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50"
          >
            <div class="text-gray-600">{{ stage.title }}</div>
            <div class="font-medium">{{ solution.soluName }}</div>
            <div class="text-gray-600">
              {{ solution.weekCount }}周 ({{ (solution.soluType === 1 ? solution.dayCount : solution.restDayCount) || 0 }}天)
            </div>
            <div class="text-gray-500 text-sm">
              <div v-if="solution.soluSummary?.length">
                {{ solution.soluSummary.join(', ') }}
              </div>
              <div v-if="solution.comment" class="mt-1 text-xs">
                {{ solution.comment }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

// 定义 Props 接口
interface Props {
  anchorItems?: Array<{
    title: string;
    href: string;
    children: Array<{
      title: string;
      href: string;
      weekCount: number;
      days: number | null;
      soluSummary: string[];
    }>;
  }>;
}

// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[];
    comment: string;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  anchorItems: () => []
});

// 响应式数据
const activeStageId = ref<string>('');
const highlightStageId = ref<string>('');
const tableContentRef = ref<HTMLElement>();

// 时间线节点颜色配置
const timelineColors = ['#868686', '#FFA438', '#638CFD', '#AB9EFF'];

// 测试数据
const mockData: TimelineStage[] = [
  {
    id: 'stage1',
    title: '诱导缓解阶段',
    solutions: [
      {
        solutionId: 'solution1',
        soluName: '诱导方案A',
        weekCount: 4,
        dayCount: 28,
        restDayCount: 28,
        soluType: 1,
        soluSummary: ['柔红霉素', '长春新碱', '门冬酰胺酶'],
        comment: '标准诱导方案'
      },
      {
        solutionId: 'solution2',
        soluName: '诱导方案B',
        weekCount: 6,
        dayCount: 42,
        restDayCount: 42,
        soluType: 1,
        soluSummary: ['阿糖胞苷', '依托泊苷'],
        comment: '强化诱导方案'
      }
    ]
  },
  {
    id: 'stage2',
    title: '巩固治疗阶段',
    solutions: [
      {
        solutionId: 'solution3',
        soluName: '巩固方案',
        weekCount: 8,
        dayCount: 56,
        restDayCount: 56,
        soluType: 1,
        soluSummary: ['甲氨蝶呤', '6-巯基嘌呤'],
        comment: '维持巩固治疗'
      }
    ]
  },
  {
    id: 'stage3',
    title: '维持治疗阶段',
    solutions: [
      {
        solutionId: 'solution4',
        soluName: '维持方案',
        weekCount: 52,
        dayCount: 364,
        restDayCount: 364,
        soluType: 1,
        soluSummary: ['甲氨蝶呤', '6-巯基嘌呤', '长春新碱'],
        comment: '长期维持治疗'
      }
    ]
  },
  {
    id: 'stage4',
    title: '随访观察阶段',
    solutions: [
      {
        solutionId: 'solution5',
        soluName: '随访方案',
        weekCount: 104,
        dayCount: 728,
        restDayCount: 728,
        soluType: 2,
        soluSummary: ['定期检查', '影像学评估'],
        comment: '停药后随访观察'
      }
    ]
  }
];

// 转换数据格式
const timelineData = computed<TimelineStage[]>(() => {
  // 如果有真实数据，使用真实数据；否则使用测试数据
  if (props.anchorItems && props.anchorItems.length > 0) {
    return props.anchorItems.map(item => ({
      id: item.href.replace('#', '').replace('.stage', ''),
      title: item.title,
      solutions: item.children.map(child => ({
        solutionId: child.href.replace('#', '').replace('.program', ''),
        soluName: child.title,
        weekCount: child.weekCount,
        dayCount: child.days,
        restDayCount: child.days,
        soluType: 1,
        soluSummary: child.soluSummary || [],
        comment: ''
      }))
    }));
  }
  return mockData;
});

// 获取时间线节点颜色
const getTimelineColor = (index: number): string => {
  return timelineColors[index % timelineColors.length];
};

// 获取时间线节点类型
const getTimelineType = (index: number): 'default' | 'info' | 'success' | 'warning' | 'error' => {
  const types = ['default', 'warning', 'info', 'success'] as const;
  return types[index % types.length];
};

// 处理时间线点击事件
const handleTimelineClick = async (stageId: string) => {
  activeStageId.value = stageId;
  highlightStageId.value = stageId;

  // 滚动到对应的表格组
  await nextTick();
  const targetElement = document.getElementById(`stage-${stageId}`);
  if (targetElement && tableContentRef.value) {
    const containerRect = tableContentRef.value.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const scrollTop = tableContentRef.value.scrollTop + targetRect.top - containerRect.top;

    tableContentRef.value.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });

    // 高亮效果持续2秒后消失
    setTimeout(() => {
      highlightStageId.value = '';
    }, 2000);
  }
};

// 初始化时选中第一个节点
if (timelineData.value.length > 0) {
  activeStageId.value = timelineData.value[0].id;
}
</script>

<style scoped>
/* 时间线样式 */
.timeline-item {
  transition: all 0.3s ease;
}

.timeline-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.timeline-item-active {
  background-color: rgba(99, 140, 253, 0.1);
}

.timeline-header {
  font-weight: 500;
  color: #333;
  transition: all 0.3s ease;
}

.timeline-header-active {
  color: #638CFD;
  font-weight: 600;
}

.timeline-content {
  color: #666;
  transition: all 0.3s ease;
}

.timeline-content-active {
  color: #333;
}

/* 表格样式 */
.table-group {
  transition: all 0.3s ease;
}

.table-group-highlight {
  background-color: rgba(99, 140, 253, 0.05);
  border-left: 4px solid #638CFD;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
