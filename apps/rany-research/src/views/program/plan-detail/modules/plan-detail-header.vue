<script setup lang="ts">
import { NButton, NTag, useDialog } from 'naive-ui';
import { h, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useTabsStore } from 'component-library';
import ButtonGroup from '@/components/common/button-group.vue';

// 定义组件props
interface PlanDetailData {
  id?: string;
  name: string;
  status: number;
  diseaseName?: string;
  riskName?: string[];
  projectName?: string;
  createTime?: string;
  updateTime?: string;
}

const props = defineProps<{
  planData?: PlanDetailData;
  loading?: boolean;
}>();

const palanVisible = defineModel<string>('preview');

// 定义事件
const emit = defineEmits<{
  edit: [];
  toggleStatus: [];
  delete: [];
  copy: [];
}>();

const tabsStore = useTabsStore();
const router = useRouter();
const dialog = useDialog();

// 状态选项配置
const statusOptions = [
  { label: '草稿', value: 0 },
  { label: '已发布', value: 1 },
  { label: '已停用', value: 2 }
];

// 状态颜色映射
const statusColorMap: Record<number, 'warning' | 'success' | 'error'> = {
  0: 'warning', // 草稿
  1: 'success', // 已发布
  2: 'error' // 已停用
};

// 计算当前状态标签
const currentStatusLabel = computed(() => {
  return statusOptions.find(item => item.value === props.planData?.status)?.label || '未知';
});

// 计算停用/发布按钮文本和类型
const toggleButtonConfig = computed(() => {
  if (props.planData?.status === 1) {
    return { text: '停用', type: 'warning' as const };
  }
  return { text: '发布', type: 'success' as const };
});

// 事件处理函数
const handleEdit = () => {
  emit('edit');
};

const handleToggleStatus = () => {
  const isPublished = props.planData?.status === 1;

  dialog.warning({
    title: isPublished ? '停用' : '发布',
    content: () =>
      h('div', { class: 'text-center' }, [
        h('h1', { class: 'font-700' }, isPublished ? '您确认要停用吗?' : '是否确认发布？'),
        ...(isPublished ? [h('h1', { class: 'mt-1 text-xs' }, '停用后不可使用该计划，历史数据可以查询！')] : [])
      ]),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => emit('toggleStatus')
  });
};

const handleDelete = () => {
  dialog.warning({
    title: '删除',
    content: () =>
      h('div', { class: 'text-center' }, [
        h('h1', { class: 'font-700' }, '您确认要删除吗?'),
        h('h1', { class: 'mt-1 text-xs' }, '删除后记录不可查询！')
      ]),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => emit('delete')
  });
};

const handleCopy = () => {
  emit('copy');
};

const handleAction = (action: 'preview' | 'detail') => {
  palanVisible.value=action;
};
</script>

<template>
  <div class="plan-detail-header">
    <!-- 第一行：标题和操作按钮 -->
    <div class="flex items-center justify-between mb-4">
      <!-- 左侧标题 -->
      <div class="flex items-center">
        <h1 class="text-xl font-semibold text-gray-900 mr-4">
          {{ planData?.name || '计划详情' }}
        </h1>
      </div>

      <!-- 按钮组,预览和详情一个预览一个详情 -->
       <div class="flex items-center space-x-2">
         <ButtonGroup />
       </div>

      <!-- 右侧操作按钮 -->
      <div class="flex items-center space-x-2">
        <NButton
          size="small"
          type="primary"
          ghost
          :disabled="loading"
          @click="handleEdit"
        >
          编辑
        </NButton>

        <NButton
          size="small"
          :type="toggleButtonConfig.type"
          ghost
          :disabled="loading"
          @click="handleToggleStatus"
        >
          {{ toggleButtonConfig.text }}
        </NButton>

        <NButton
          size="small"
          type="error"
          ghost
          :disabled="loading"
          @click="handleDelete"
        >
          删除
        </NButton>

        <NButton
          size="small"
          type="info"
          ghost
          :disabled="loading"
          @click="handleCopy"
        >
          复制
        </NButton>
      </div>
    </div>

    <!-- 第二行：基础信息 -->
    <div class="flex items-center justify-between text-sm text-gray-600">
      <!-- 状态 -->
      <!-- <div class="flex items-center space-x-2">
        <span class="font-medium text-gray-700">状态:</span>
        <NTag
          :type="statusColorMap[planData?.status ?? 0]"
          size="small"
          class="px-3 py-1 font-medium"
        >
          {{ currentStatusLabel }}
        </NTag>
      </div> -->

      <!-- 疾病分型 -->
      <div class="flex items-center space-x-2">
        <span class="font-medium text-gray-700">疾病分型:</span>
        <span class="text-gray-600">{{ planData?.diseaseName || '暂无' }}</span>
      </div>


      <!-- 危险度分型 -->
      <div class="flex items-center space-x-2">

        <span class="font-medium text-gray-700">危险度分型:</span>
        <span class="text-gray-600">{{ planData?.riskName?.join(', ') || '暂无' }}</span>
      </div>


      <!-- 关联临床实验 -->
      <div class="flex items-center space-x-2">

        <span class="font-medium text-gray-700">关联临床实验:</span>
        <span class="text-gray-600">{{ planData?.projectName || '暂无' }}</span>
      </div>


      <!-- 创建时间 -->
      <div class="flex items-center space-x-2">

        <span class="font-medium text-gray-700">创建时间:</span>
        <span class="text-gray-600">{{ planData?.createTime || '暂无' }}</span>
      </div>


      <!-- 更新时间 -->
      <div class="flex items-center space-x-2">

        <span class="font-medium text-gray-700">更新时间:</span>
        <span class="text-gray-600">{{ planData?.updateTime || '暂无' }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.plan-detail-header {
  @apply bg-white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .plan-detail-header .flex.items-center.space-x-8 {
    @apply flex-wrap gap-y-2 space-x-4;
  }
}

@media (max-width: 768px) {
  .plan-detail-header .flex.items-center.justify-between {
    @apply flex-col items-start space-y-3;
  }

  .plan-detail-header .flex.items-center.space-x-2:last-child {
    @apply flex-wrap gap-2;
  }
}
</style>
